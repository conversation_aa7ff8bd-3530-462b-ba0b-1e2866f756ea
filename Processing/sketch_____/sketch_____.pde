// サイコロの状態管理変数
// 1: サイコロが動いている状態
// 2: サイコロの結果が表示されている状態
int state = 1; // 初期状態をサイコロが動いている状態にする

// サイコロの目の値
int dieResult = 0;

// サイコロのアニメーション用変数
long lastUpdateTime = 0; // 最後に更新した時間
int rollCount = 0; // 転がる回数を表現するためのカウンター
int totalRolls = 0; // サイコロを振った総回数をカウント

// プログラム開始からの経過時間を計測するための変数 (クリックでリセットしない)
long programStartTimeMillis;

// 表示するテキストの配列
String[] messages = {
  "Three times arounds!",
  "Three times jumps!",
  "Three times craps!"
};
String currentMessage = ""; // 現在表示するメッセージ
float messageDisplayChance = 0.5; // メッセージが表示される確率 (0.0 から 1.0)

void setup() {
  size(400, 400);
  background(220); // 画面の背景色
  textAlign(CENTER, CENTER); // テキストを中央揃えに
  textSize(64); // サイコロの目の文字サイズ

  // プログラム開始と同時にサイコロが動く状態にするための初期設定
  dieResult = (int)random(1, 7); // 最初の目を設定
  
  // プログラム開始時間を記録 (一度だけ)
  programStartTimeMillis = millis();
}

void draw() {
  background(220); // 毎フレーム背景をクリア

  if (state == 1) {
    // サイコロが動いている状態
    long currentTime = millis();
    if (currentTime - lastUpdateTime > 50) { // 50ミリ秒ごとに更新
      dieResult = (int)random(1, 7); // 通常のランダムな目を表示
      lastUpdateTime = currentTime;
      rollCount++; // 転がる回数をインクリメント
    }
    
    fill(0);
    text(dieResult, width / 2, height / 2); // 現在のランダムな目を表示
    
  } else if (state == 2) {
    // サイコロの結果が表示されている状態
    fill(0, 0, 255); // 結果は青色で表示
    text(dieResult, width / 2, height / 2); // 最終的な目を表示

    // 3回ごとにメッセージを表示
    if (totalRolls % 3 == 0 && currentMessage != "") {
      fill(100, 50, 0); // メッセージの文字色
      textSize(28); // メッセージの文字サイズ
      text(currentMessage, width / 2, height * 0.75); // 画面下部に表示
      textSize(64); // サイコロの目の文字サイズに戻す
    }
  }
}

void mousePressed() {
  if (state == 1) {
    // サイコロが動いている途中でクリックされた場合、結果表示へ遷移
    state = 2;
    dieResult = (int)random(1, 7); // 最終的な目を元のランダムに戻す
    totalRolls++; // サイコロを振った総回数をインクリメント

    // 3回ごとにランダムなメッセージを選択
    if (totalRolls % 3 == 0) {
      int randomIndex = (int)random(messages.length);
      currentMessage = messages[randomIndex];
    } else {
      currentMessage = ""; // 3回ごとでなければメッセージをクリア
    }

  } else if (state == 2) {
    // 結果表示状態からサイコロが動く状態へ戻す
    state = 1;
    lastUpdateTime = millis();  // アニメーション開始時間をリセット
    rollCount = 0;              // カウンターをリセット
  }
}
