{"cells": [{"cell_type": "code", "execution_count": 3, "id": "84190747", "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'pyo<PERSON>'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[3], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01menviron\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;241m*\u001b[39m\n\u001b[1;32m      3\u001b[0m \u001b[38;5;66;03m# モデル定義\u001b[39;00m\n\u001b[1;32m      4\u001b[0m model \u001b[38;5;241m=\u001b[39m ConcreteModel()\n", "\u001b[0;31mModuleNotFoundError\u001b[0m: No module named 'pyomo'"]}], "source": ["from pyomo.environ import *\n", "\n", "# モデル定義\n", "model = ConcreteModel()\n", "\n", "# セット定義\n", "model.Factory = Set(initialize=['a'])\n", "model.DC = Set(initialize=['b1', 'b2'])\n", "model.Demand = Set(initialize=['c1', 'c2', 'c3', 'c4', 'c5'])\n", "\n", "# パラメータ定義\n", "cdf_data = {('a', 'b1'): 2.1, ('a', 'b2'): 3.2}\n", "cds_data = {\n", "    ('b1', 'c1'): 1.3,  ('b1', 'c2'): 0.7,  ('b1', 'c3'): 1.1,  ('b1', 'c4'): 10000, ('b1', 'c5'): 10000,\n", "    ('b2', 'c1'): 10000, ('b2', 'c2'): 1.3, ('b2', 'c3'): 1.0,  ('b2', 'c4'): 0.8,    ('b2', 'c5'): 1.5\n", "}\n", "afd_data = {('a', 'b1'): 250, ('a', 'b2'): 250}\n", "ads_data = {\n", "    ('b1', 'c1'): 100, ('b1', 'c2'): 100, ('b1', 'c3'): 100, ('b1', 'c4'): 0,   ('b1', 'c5'): 0,\n", "    ('b2', 'c1'): 0,   ('b2', 'c2'): 200, ('b2', 'c3'): 100, ('b2', 'c4'): 100, ('b2', 'c5'): 200\n", "}\n", "sp_data = {'a': 400}\n", "d_data = {'c1': 90, 'c2': 70, 'c3': 80, 'c4': 60, 'c5': 70}\n", "\n", "model.Cdf = Param(model.Factory, model.DC, initialize=cdf_data)\n", "model.Cds = Param(model.DC, model.Demand, initialize=cds_data)\n", "model.aFD = Param(model.Factory, model.DC, initialize=afd_data)\n", "model.aDS = Param(model.DC, model.Demand, initialize=ads_data)\n", "model.sp = Param(model.Factory, initialize=sp_data)\n", "model.d = Param(model.Demand, initialize=d_data)\n", "\n", "# 変数定義\n", "model.x = Var(model.Factory, model.DC, domain=NonNegativeIntegers)\n", "model.y = Var(model.DC, model.Demand, domain=NonNegativeIntegers)\n", "\n", "# 目的関数\n", "def cost_rule(m):\n", "    return sum(m.Cdf[f,h]*m.x[f,h] for f in m.Factory for h in m.DC) + \\\n", "           sum(m.Cds[h,s]*m.y[h,s] for h in m.DC for s in m.Demand)\n", "model.Cost = Objective(rule=cost_rule, sense=minimize)\n", "\n", "# 制約\n", "def supply_constraint(m, f):\n", "    return sum(m.x[f,h] for h in m.DC) == m.sp[f]\n", "model.SupplyConstraint = Constraint(model.Factory, rule=supply_constraint)\n", "\n", "def demand_constraint(m, s):\n", "    return sum(m.y[h,s] for h in m.DC) == m.d[s]\n", "model.DemandConstraint = Constraint(model.Demand, rule=demand_constraint)\n", "\n", "def factory_dc_capacity(m, f, h):\n", "    return m.x[f,h] <= m.aFD[f,h]\n", "model.CapacityFD = Constraint(model.Factory, model.DC, rule=factory_dc_capacity)\n", "\n", "def dc_demand_capacity(m, h, s):\n", "    return m.y[h,s] <= m.aDS[h,s]\n", "model.CapacityDS = Constraint(model.DC, model.Demand, rule=dc_demand_capacity)\n", "\n", "def dc_flow_balance(m, h):\n", "    return sum(m.x[f,h] for f in m.Factory) >= sum(m.y[h,s] for s in m.Demand)\n", "model.FlowBalance = Constraint(model.DC, rule=dc_flow_balance)\n", "\n", "# ソルバー実行\n", "solver = SolverFactory('glpk')  # glpk or 'cbc', 'gurobi', etc.\n", "result = solver.solve(model, tee=True)\n", "\n", "# 結果表示\n", "print(f\"\\nTotal Cost: {value(model.Cost)}\\n\")\n", "\n", "print(\"x[f,h] values (Factory → DC):\")\n", "for f in model.Factory:\n", "    for h in model.DC:\n", "        val = value(model.x[f,h])\n", "        if val > 0:\n", "            print(f\"  {f} → {h}: {val}\")\n", "\n", "print(\"\\ny[h,s] values (DC → Demand):\")\n", "for h in model.DC:\n", "    for s in model.Demand:\n", "        val = value(model.y[h,s])\n", "        if val > 0:\n", "            print(f\"  {h} → {s}: {val}\")\n"]}, {"cell_type": "code", "execution_count": 1, "id": "580c3e68", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting glpk\n", "  Using cached glpk-0.4.8.tar.gz (160 kB)\n", "  Installing build dependencies ... \u001b[?25ldone\n", "\u001b[?25h  Getting requirements to build wheel ... \u001b[?25ldone\n", "\u001b[?25h  Preparing metadata (pyproject.toml) ... \u001b[?25ldone\n", "\u001b[?25hBuilding wheels for collected packages: glpk\n", "  Building wheel for glpk (pyproject.toml) ... \u001b[?25lerror\n", "  \u001b[1;31merror\u001b[0m: \u001b[1msubprocess-exited-with-error\u001b[0m\n", "  \n", "  \u001b[31m×\u001b[0m \u001b[32mBuilding wheel for glpk \u001b[0m\u001b[1;32m(\u001b[0m\u001b[32mpyproject.toml\u001b[0m\u001b[1;32m)\u001b[0m did not run successfully.\n", "  \u001b[31m│\u001b[0m exit code: \u001b[1;36m1\u001b[0m\n", "  \u001b[31m╰─>\u001b[0m \u001b[31m[26 lines of output]\u001b[0m\n", "  \u001b[31m   \u001b[0m /private/var/folders/_p/gpx29nx95pn__7720hyny3sm0000gn/T/pip-build-env-6igy_ylf/overlay/lib/python3.12/site-packages/setuptools/dist.py:759: SetuptoolsDeprecationWarning: License classifiers are deprecated.\n", "  \u001b[31m   \u001b[0m !!\n", "  \u001b[31m   \u001b[0m \n", "  \u001b[31m   \u001b[0m         ********************************************************************************\n", "  \u001b[31m   \u001b[0m         Please consider removing the following classifiers in favor of a SPDX license expression:\n", "  \u001b[31m   \u001b[0m \n", "  \u001b[31m   \u001b[0m         License :: OSI Approved :: GNU General Public License (GPL)\n", "  \u001b[31m   \u001b[0m \n", "  \u001b[31m   \u001b[0m         See https://packaging.python.org/en/latest/guides/writing-pyproject-toml/#license for details.\n", "  \u001b[31m   \u001b[0m         ********************************************************************************\n", "  \u001b[31m   \u001b[0m \n", "  \u001b[31m   \u001b[0m !!\n", "  \u001b[31m   \u001b[0m   self._finalize_license_expression()\n", "  \u001b[31m   \u001b[0m running bdist_wheel\n", "  \u001b[31m   \u001b[0m running build\n", "  \u001b[31m   \u001b[0m running build_ext\n", "  \u001b[31m   \u001b[0m building 'glpk' extension\n", "  \u001b[31m   \u001b[0m creating build/temp.macosx-11.1-arm64-cpython-312/src\n", "  \u001b[31m   \u001b[0m clang -fno-strict-overflow -Wsign-compare -Wunreachable-code -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /opt/anaconda3/include -arch arm64 -fPIC -O2 -isystem /opt/anaconda3/include -arch arm64 -DVERSION_NUMBER=\\\"0.4.8\\\" -I/opt/anaconda3/include/python3.12 -c src/2to3.c -o build/temp.macosx-11.1-arm64-cpython-312/src/2to3.o\n", "  \u001b[31m   \u001b[0m clang -fno-strict-overflow -Wsign-compare -Wunreachable-code -DNDEBUG -O2 -Wall -fPIC -O2 -isystem /opt/anaconda3/include -arch arm64 -fPIC -O2 -isystem /opt/anaconda3/include -arch arm64 -DVERSION_NUMBER=\\\"0.4.8\\\" -I/opt/anaconda3/include/python3.12 -c src/bar.c -o build/temp.macosx-11.1-arm64-cpython-312/src/bar.o\n", "  \u001b[31m   \u001b[0m In file included from src/bar.c:22:\n", "  \u001b[31m   \u001b[0m src/lp.h:24:10: fatal error: 'glpk.h' file not found\n", "  \u001b[31m   \u001b[0m    24 | #include <glpk.h>\n", "  \u001b[31m   \u001b[0m       |          ^~~~~~~~\n", "  \u001b[31m   \u001b[0m 1 error generated.\n", "  \u001b[31m   \u001b[0m error: command '/usr/bin/clang' failed with exit code 1\n", "  \u001b[31m   \u001b[0m \u001b[31m[end of output]\u001b[0m\n", "  \n", "  \u001b[1;35mnote\u001b[0m: This error originates from a subprocess, and is likely not a problem with pip.\n", "\u001b[?25h\u001b[31m  ERROR: Failed building wheel for glpk\u001b[0m\u001b[31m\n", "\u001b[0mFailed to build glpk\n", "\u001b[31mERROR: ERROR: Failed to build installable wheels for some pyproject.toml based projects (glpk)\u001b[0m\u001b[31m\n", "\u001b[0mNote: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install glpk"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}