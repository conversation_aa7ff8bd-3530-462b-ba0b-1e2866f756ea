{"cells": [{"cell_type": "markdown", "id": "fb147fc3", "metadata": {}, "source": ["# 研究ノート"]}, {"cell_type": "markdown", "id": "74fd576d", "metadata": {}, "source": ["## メモ\n", "- 在庫がマイナスになる（需要を満たせない）ことも許容したい　→　先行研究との違い①  \n", "    - 段取り替えコスト（時間・費用）が高コストの工程では，あえて需要に応えない→作らない→段取り替えしないことが良い結果になる可能性もあるのでは？  \n", "    - バックオーダーコストの導入  \n", "- $I_{i0}$の与え方の検討　→　先行研究との違い②  "]}, {"cell_type": "markdown", "id": "bca680c1", "metadata": {}, "source": ["## や<PERSON>こと\n", "- 初期在庫更新のコードを作る  \n", "- その最良初期在庫で定式化①を実施  \n", "- 定式化②のコードを作る  "]}, {"cell_type": "markdown", "id": "4fa47832", "metadata": {}, "source": ["## 先行研究\n", "期末在庫量の分布型 : 生産・在庫システムの研究(1)（平川）  \n", "URL：https://www.jstage.jst.go.jp/article/jimapre/26/1/26_KJ00001728234/_article/-char/ja/  \n", "$t$期で$I_{it}$が負になった時，$t+1$期に持ち越される．  \n", "\n", "残業生産を考慮したロットサイズ決定問題に対するアニーリング・ヒューリスティック（森川・中村）  \n", "URL：https://www.jstage.jst.go.jp/article/kikaic1979/59/558/59_558_611/_article/-char/ja/  \n", "$I_{i0}=0$としている．  \n", "\n", "SCMにおける平準化生産・発注の最適化とその効果に関する研究（田村・大野）  \n", "URL: https://www.jstage.jst.go.jp/article/kikaic/79/798/79_162/_pdf  \n", "\n", "シミュレーションによる最適在庫水準決定アルゴリズムの性能評価に関する研究（田村）  \n", "URL: https://www.jstage.jst.go.jp/article/jsmemsd/2015/0/2015_99/_pdf.  \n", "最適な$I_{i0}$を求めるためのアルゴリズムを提案している．"]}, {"cell_type": "markdown", "id": "cbcec2c1", "metadata": {}, "source": ["## 日々スケジューリングの定式化\n", "\n", "### 定数\n", "各品番$i \\quad i = 1, \\dots, m$  \n", "各期間$t \\quad t = 1, \\dots, n$  \n", "品番$i$の単位在庫コスト$v_{i}$  \n", "品番$i$の単位製造コスト$c_{i}$  \n", "品番$i$の単位段取り替えコスト$s_{i}$  \n", "品番$i$の単位機会損失コスト$h_{i}$  \n", "期間$t$，品番$i$の機会損失量$g_{it}$  \n", "期間$t$，品番$i$の需要量$d_{it}$  \n", "期間$t$，品番$i$の単位売上$r_{it}$  \n", "品番$i$を1単位生産するための必要時間$a_{i}$  \n", "品番$i$へ段取り替えするための必要時間$b_{i}$  \n", "最大負荷時間$T$  \n", "最大段取り替え回数$U$  \n", "最小生産数を表す定数$\\epsilon$  \n", "最大生産数を表す定数$\\zeta$  \n", "\n", "### 決定変数\n", "期間$t$，品番$i$へ段取り替えするときに1を取る2値変数$x_{it} \\in {0,1}$  \n", "期間$t$，品番$i$の生産量$p_{it}$  \n", "期間$t$，品番$i$の在庫量$I_{it} = I_{i(t-1)} + p_{it} - d_{it}$  \n", "\n", "### 目的関数\n", "$\\max \\sum_{i=1}^m \\sum_{t=1}^n (d_{it}r_{it} - \\{v_{i}I_{it} + c_{i}p_{it} + s_{i}x_{it} + h_{i}g_{it} \\})$  \n", "\n", "\n", "### 制約条件\n", "(1) 総生産時間と総段取り替え時間は最大負荷時間以下  \n", "(2) 総段取り替え回数は最大段取り替え回数以下  \n", "(3) 在庫量の式  \n", "(4) 機会損失量の式  \n", "(5) 生産量は非負  \n", "(6) 段取り替えは0, 1\n", "\n", "\n", "$\\sum_{i=1}^m a_{i}p_{it} + \\sum_{i=1}^m b_{i}x_{it} \\leq T \\quad \\forall t$  \n", "$\\sum_{i=1}^m x_{it} \\leq U \\quad \\forall t$  \n", "$I_{it} = I_{i(t-1)} + p_{it} - d_{it} \\quad \\forall i, t$  \n", "$g_{it} = \\max \\{0, d_{it} - I_{it} \\} \\quad \\forall i, t$  \n", "$\\epsilon x_{it} \\leq p_{it} \\leq \\zeta x_{it} \\quad \\forall i, t$  \n", "$p_{it} \\geq 0 \\quad \\forall i, t$  \n", "$x_{it} \\in {0,1} \\quad \\forall i, t$  "]}, {"cell_type": "markdown", "id": "9dbf3192", "metadata": {}, "source": ["## SCMの定式化\n", "論文から  \n", "平滑化係数を削除：生産指示量に平滑化係数を適用せず，バックオーダーと新規出荷要求の合計を生産指示とする（かんばん方式のまま）．  \n", "段取り時間の導入：品目切り替え時に段取り時間を考慮する．  \n", "不良率の導入：生産着手量に対して一定の不良率を考慮する．  \n", "（モデルの実行には遺伝的アルゴリズム（GA）を使用する．）  \n", "\n", "### 定式化\n", "追加される定数  \n", "工程$m$における品番$i$の段取り時間$s_{i,m}$  \n", "工程$m$における品番$i$の単位段取りコスト$cs_{i,m}$  \n", "工程$m$における品番$i$の不良品発生率$r_{i,m}$  \n", "工程$m$，$t$期で生産着手された品番$i$の良品の量$X'_{i,m,t}$  \n", "工程$m$，$t$期で発生した総段取り時間$U_{m,t}$  \n", "\n", "追加される変数  \n", "工程$m$，$t$期に品番$i$が生産される場合に，1を取るバイナリー変数$\\delta_{i,m,t}$  \n", "\n", "### モデルの更新\n", "完成品期首在庫量：  \n", "$H_{i,m,t} = I_{i,m,t−1} + X'_{i,m,t−a_m}$  \n", "\n", "新規の出荷要求量：  \n", "$Q_{i,m,t} = O_{i,m+1,t−(b_{m+1}-w_{m+1})}, \\quad m = 1, \\ldots ,M−1$  \n", "$Q_{i,M,t} = D_{i,t−1}$  \n", "\n", "次工程への出荷要求量：  \n", "$R_{i,m,t} = B_{i,m,t−1}+Q_{i,m,t}$  \n", "\n", "次工程への出荷量（＝次工程での入荷量）：  \n", "$Y_{i,m,t} = min(R_{i,m−1,t},H_{i,m−1,t})$  \n", "$Y_{i,1,t} = O_{i,1,t−(b_1-w_1)}$  \n", "\n", "資材の期首在庫量：  \n", "$K_{i,m,t} = J_{i,m,t−1}+Y_{im,t−w_m}$  \n", "\n", "完成品の期末在庫量：  \n", "$I_{i,m,t} = H_{i,m,t}−Y_{i,m+1,t}$  \n", "\n", "完成品のバックオーダー量：  \n", "$B_{i,m,t} = R_{i,m,t} − Y_{i,m+1,t}$  \n", "\n", "生産指示量：  \n", "$P_{i,m,t} = S_{i,m,t−1}+Q_{i,m,t}$  \n", "\n", "生産プライオリティと生産順序の決定  \n", "$P_{i,m,t} = \\frac{S_{i,m,t−1}+Q_{i,m,t}}{D_i}$  \n", "\n", "工程$m$，$t$期の総段取り時間  \n", "$U_{m,t} = \\sum_{i \\in P_{m,t}}s_{i,m} \\cdot \\delta_{i,m,t}$  \n", "\n", "生産能力と生産着手量  \n", "$C'_{m,t} = C_{m,t} − U_{m,t}$  \n", "$X_{i,m,t} = min(P_{i,m,t}, K_{i,m,t}, C'_{i,m,t})$  \n", "\n", "実際に完成する良品の量  \n", "$X'_{i,m,t} = X_{i,m,t} \\cdot (1 - r_{i,m})$  \n", "\n", "生産の遅れ（生産バックオーダー量）  \n", "$S_{i,m,t} = S_{i,m,t−1}+Q_{i,m,t}−X_{i,m,t}$  \n", "\n", "資材不足量（資材バックオーダー量）  \n", "$A_{i,m,t} = min(P_{i,m,t},C'_{i,m,t},X_{i,m,t})$  \n", "\n", "資材期末在庫量  \n", "$J_{i,m,t} = K_{i,m,t} − X_{i,m,t}$  \n", "\n", "前工程への資材発注量  \n", "$O_{i,m,t} = X_{i,m,t}$  \n", "\n", "残業時間  \n", "$\\sum_i X_{i,m,t} \\leq C^{(r)}_{m,t} + C^{(o)}_{m,t}$  \n", "$C^{(o)}_{m,t} \\leq V_{m,t}$  \n", "残業時間$C^{(o)}_{m,t}$  \n", "上限時間$V_{m,t}$  \n", "定時稼働時間$C^{(r)}_{m,t}$  \n", "\n", "目的関数  \n", "$minimize \\quad \\lim_{T \\to \\infty} z$  \n", "$z = \\frac{1}{T} \\sum_{t=1}^T \\left( \\sum_{i,m} h^{(u)}_{i,m} J_{i,m,t} + \\sum_{i,m} h^{(v)}_{i,m} I_{i,m,t} + \\sum_{i,m} c^{(u)}_{i,m} A_{i,m,t} + \\sum_{i,m} c^{(v)}_{i,m} B_{i,m,t} + \\sum_{m} c_m^{(o)} o_m C^{(o)}_{m,t} \\right)$  \n", "資材の単位保管費用$h^{(u)}_{i,m}$  \n", "完成品の単位保管費用$h^{(v)}_{i,m}$  \n", "資材の単位バックオーダー費用$c^{(u)}_{i,m}$  \n", "完成品の単位バックオーダー費用$c^{(v)}_{i,m}$  \n", "単位残業費用$c_m^{(o)}$  "]}, {"cell_type": "code", "execution_count": null, "id": "404fd61f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}